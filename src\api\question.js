import request from '@/utils/request'

// 题目列表
export function pageQueryQuestionList(data) {
  return request({
    // 文档未提供URL，根据其他接口推断
    url: '/question/pageQueryQuestionList',
    method: 'post',
    data
  })
}

// 添加题目
export function addQuestion(data) {
  return request({
    // 文档未提供URL，根据其他接口推断
    url: '/question/addQuestion',
    method: 'post',
    data
  })
}

// 删除题目
export function deleteQuestion(data) {
  return request({
    url: '/question/deleteQuestion',
    method: 'post', // 文档未明确，根据统一性，使用POST
    data // { questionId }
  })
}

// 审核题目
export function auditQuestion(data) {
  return request({
    url: '/question/auditQuestion',
    method: 'post',
    data // { questionId, status }
  })
}

// 更新题目
export function updateQuestion(data) {
  return request({
    url: '/question/updateQuestion',
    method: 'post', // 文档未明确，根据统一性，使用POST
    data
  })
}