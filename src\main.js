import { createApp } from 'vue'
import App from './App.vue'

// 引入 Pinia
import { createPinia } from 'pinia'

// 引入 Vue Router
import router from './router'

// 引入 Element Plus 图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 引入全局样式 (如果需要)
import './assets/main.css'

// 开发环境下引入mock
if (import.meta.env.DEV) {
  import('../mock/index.js')
}

const app = createApp(App)

// 注册所有 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia()) // 使用 Pinia
app.use(router)      // 使用 Vue Router

app.mount('#app')
