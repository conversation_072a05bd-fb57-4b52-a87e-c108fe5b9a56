<!-- src/layout/components/SidebarItem.vue -->
<template>
  <!-- 直接渲染菜单项，不再需要判断和递归 -->
  <el-menu-item v-if="!item.meta?.hidden" :index="resolvePath(item.path)">
    <el-icon v-if="item.meta?.icon"><component :is="item.meta.icon" /></el-icon>
    <template #title>{{ item.meta?.title }}</template>
  </el-menu-item>
</template>

<script setup>
import path from 'path-browserify';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
});

// 解析路由路径，确保得到完整的绝对路径
const resolvePath = (routePath) => {
  return path.resolve(props.basePath, routePath);
};
</script>

<script>
// 为了让 <component :is="..."> 能正确解析字符串名称的图标
export default {
  components: { ...ElementPlusIconsVue }
}
</script>