import Mock from 'mockjs'

const userList = Mock.mock({
  'list|25': [{
    'id|+1': 1,
    userPhone: /^1[3-9]\d{9}$/,
    name: '@cname',
    'userRole|1': ['管理员', '普通用户', '访客'],
    'perType|1': ['A', 'B', 'C']
  }]
}).list

// 用户列表
Mock.mock('/web/user/pageQueryUserList', 'post', (options) => {
  const { pageNum = 1, pageSize = 10 } = JSON.parse(options.body)
  const list = userList.slice((pageNum - 1) * pageSize, pageNum * pageSize)
  return {
    code: '200',
    resData: list,
    msg: '查询成功'
  }
})

// 更新用户
Mock.mock('/web/user/updateUser', 'post', () => {
  return {
    code: '200',
    resData: null,
    msg: '更新成功'
  }
})

// 获取验证码
Mock.mock('/web/login/getVerifyCode', 'post', () => {
  return {
    code: '200',
    resData: Mock.Random.string('number', 6), // 返回6位数字验证码
    msg: '验证码已发送'
  }
})

// web端登录
Mock.mock('/web/login/webLogin', 'post', () => {
  return {
    code: '200',
    resData: {
      userId: Mock.Random.id(),
      accessToken: Mock.Random.guid(),
      refreshToken: Mock.Random.guid()
    },
    msg: '登录成功'
  }
})

// /user/getUserInfo
Mock.mock('/web/user/getUserInfo', 'get', () => {
  return {
    code: '200',
    resData: {
      id: Mock.Random.id(),
      nickName: '管理员',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      role: 'super_admin',
      defaultCategoryId: 1
    },
    msg: '查询成功'
  }
})