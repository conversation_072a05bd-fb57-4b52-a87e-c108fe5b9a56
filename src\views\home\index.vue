<template>
  <div class="home-container">
    <el-card class="welcome-card">
      <template #header>
        <div class="card-header">
          <h2>欢迎来到刻意练后台管理系统</h2>
        </div>
      </template>
      <div class="welcome-content">
        <el-row :gutter="20">
          <el-col :span="8" v-for="route in guideRoutes" :key="route.path">
            <el-card class="stat-card" shadow="hover" @click="handleNavigate(route.path)">
              <div class="stat-item">
                <el-icon size="40" :color="getIconColor(route.meta.icon)">
                  <!-- 动态渲染图标 -->
                  <component :is="icons[route.meta.icon]" />
                </el-icon>
                <div class="stat-info">
                  <h3>{{ route.meta.title }}</h3>
                  <p>{{ route.meta.description }}</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { usePermissionStore } from '@/store/modules/permission';
// 引入所有可能用到的图标
import { User, Grid, Tickets } from '@element-plus/icons-vue';

const router = useRouter();
const permissionStore = usePermissionStore();

// 创建一个图标组件的映射，用于动态渲染
const icons = {
  User,
  Grid,
  Tickets
};

// 【核心逻辑】计算出需要在首页显示的引导路由
const guideRoutes = computed(() => {
  // 找到根布局路由
  const layoutRoute = permissionStore.routes.find(r => r.path === '/');
  if (!layoutRoute || !layoutRoute.children) {
    return [];
  }
  // 过滤出子路由中需要在首页显示的项（排除首页本身）
  // permissionStore 已确保这里的路由都是当前用户有权限的
  return layoutRoute.children.filter(
    route => route.name !== 'Home' && route.meta.description
  );
});

// 点击卡片跳转
const handleNavigate = path => {
  // 子路由的 path 是相对路径，需要拼接成完整路径
  router.push(`/${path}`);
};

// 根据图标名称返回不同颜色，美化界面
const getIconColor = iconName => {
  switch (iconName) {
    case 'User':
      return '#409EFF';
    case 'Grid':
      return '#67C23A';
    case 'Tickets':
      return '#E6A23C';
    default:
      return '#909399';
  }
};
</script>

<style scoped>
.home-container {
  padding: 20px;
}
.welcome-card {
  border-radius: 8px;
}
.card-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}
.welcome-content {
  padding-top: 10px;
}
.stat-card {
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}
.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.stat-item {
  display: flex;
  align-items: center;
}
.stat-info {
  margin-left: 20px;
}
.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
}
.stat-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}
</style>