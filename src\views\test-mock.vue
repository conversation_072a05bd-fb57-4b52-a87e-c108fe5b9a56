<template>
  <div class="test-mock">
    <h1>Mock 接口测试页面</h1>
    
    <el-card class="test-card">
      <template #header>
        <span>用户登录测试</span>
      </template>
      <el-form :model="loginForm" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="loginForm.username" placeholder="admin 或 user1"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="loginForm.password" type="password" placeholder="123456"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testLogin">测试登录</el-button>
        </el-form-item>
      </el-form>
      <div v-if="loginResult" class="result">
        <h4>登录结果：</h4>
        <pre>{{ JSON.stringify(loginResult, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>用户列表测试</span>
      </template>
      <el-button type="primary" @click="testUserList">获取用户列表</el-button>
      <div v-if="userListResult" class="result">
        <h4>用户列表结果：</h4>
        <pre>{{ JSON.stringify(userListResult, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>分类列表测试</span>
      </template>
      <el-button type="primary" @click="testCategoryList">获取分类列表</el-button>
      <div v-if="categoryListResult" class="result">
        <h4>分类列表结果：</h4>
        <pre>{{ JSON.stringify(categoryListResult, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>问题列表测试</span>
      </template>
      <el-button type="primary" @click="testQuestionList">获取问题列表</el-button>
      <div v-if="questionListResult" class="result">
        <h4>问题列表结果：</h4>
        <pre>{{ JSON.stringify(questionListResult, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { login, getUserList } from '@/api/user'
import { pageQueryQuestionList } from '@/api/category'
import { pageQueryQuestionList } from '@/api/question'

const loginForm = ref({
  username: 'admin',
  password: '123456'
})

const loginResult = ref(null)
const userListResult = ref(null)
const categoryListResult = ref(null)
const questionListResult = ref(null)

const testLogin = async () => {
  try {
    const result = await login(loginForm.value)
    loginResult.value = result
    ElMessage.success('登录测试成功')
  } catch (error) {
    ElMessage.error('登录测试失败：' + error.message)
  }
}

const testUserList = async () => {
  try {
    const result = await getUserList({ page: 1, pageSize: 10 })
    userListResult.value = result
    ElMessage.success('用户列表测试成功')
  } catch (error) {
    ElMessage.error('用户列表测试失败：' + error.message)
  }
}

const testCategoryList = async () => {
  try {
    const result = await pageQueryQuestionList({ page: 1, pageSize: 10 })
    categoryListResult.value = result
    ElMessage.success('分类列表测试成功')
  } catch (error) {
    ElMessage.error('分类列表测试失败：' + error.message)
  }
}

const testQuestionList = async () => {
  try {
    const result = await pageQueryQuestionList({ page: 1, pageSize: 10 })
    questionListResult.value = result
    ElMessage.success('问题列表测试成功')
  } catch (error) {
    ElMessage.error('问题列表测试失败：' + error.message)
  }
}
</script>

<style scoped>
.test-mock {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.result {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.result pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 