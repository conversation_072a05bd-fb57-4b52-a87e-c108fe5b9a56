<template>
  <el-container class="layout-container">
    <Sidebar />
    <el-container direction="vertical">
      <AppHeader />
      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import Sidebar from './components/Sidebar.vue'
import AppHeader from './components/AppHeader.vue'
</script>

<style scoped>
.layout-container {
  height: 100vh;
}
.el-main {
  background-color: #f0f2f5;
  padding: 20px;
}
</style> 