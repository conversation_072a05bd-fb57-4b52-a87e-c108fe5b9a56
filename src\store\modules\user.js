// src/store/modules/user.js
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { webLogin as loginApi, getUserInfo } from '@/api/user';
import router from '@/router';
import { usePermissionStore } from './permission'; // 导入 permission store

export const useUserStore = defineStore('user', () => {
    const token = ref(localStorage.getItem('token') || '');
    const userInfo = ref(JSON.parse(localStorage.getItem('userInfo')) || null);

    async function login(loginData) {
        debugger;
        const response = await loginApi(loginData);
        token.value = response.token;
        localStorage.setItem('token', response.token);
        // 不再需要在这里获取用户信息或跳转，守卫会做
    }

    async function fetchUserInfo() {
        const response = await getUserInfo();
        userInfo.value = response;
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value));
        return response; // 返回用户信息，让守卫能拿到
    }

    async function logout() {
        const permissionStore = usePermissionStore();
        token.value = '';
        userInfo.value = null;
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        permissionStore.resetRoutes(); // 重置权限路由
        // location.reload(); // 推荐使用重载页面来清理所有状态
        router.push('/login');
    }

    return { token, userInfo, login, logout, fetchUserInfo };
});