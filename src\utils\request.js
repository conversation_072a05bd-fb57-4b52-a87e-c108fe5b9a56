import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'

// 创建 axios 实例
const service = axios.create({
    baseURL: import.meta.env.VITE_APP_BASE_API || '/web', // API 的 base_url 已更新为 /web
    timeout: 5000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        const userStore = useUserStore()
        // 按照新接口文档，登录后返回 accessToken
        if (userStore.token) {
            // 后端需要accessToken字段
            config.headers['accessToken'] = userStore.token
        }
        return config
    },
    error => {
        console.log(error) // for debug
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        const res = response.data
        // 根据新的后端接口规范调整
        // 假设成功 code 为 '200' 或 200，或是一个表示成功的特定字符串如 'SUCCESS'
        // 为通用起见，我们假设非成功时 code 会有具体错误码
        if (res.code && res.code !== 200 && res.code !== '200' && res.code.toUpperCase() !== 'SUCCESS') {
            ElMessage({
                message: res.msg || 'Error', // 字段由 message -> msg
                type: 'error',
                duration: 5 * 1000
            })
            // 比如 token 失效 (假设错误码为 401)
            if (res.code === 401 || res.code === '401') {
                 const userStore = useUserStore()
                 userStore.logout().then(() => {
                    location.reload() // 重新实例化 vue-router 对象 避免 bug
                 })
            }
            return Promise.reject(new Error(res.msg || 'Error'))
        } else {
            return res.resData // 返回 res.resData，而不是整个 response 或 res.data
        }
    },
    error => {
        console.log('err' + error) // for debug
        ElMessage({
            message: error.message,
            type: 'error',
            duration: 5 * 1000
        })
        return Promise.reject(error)
    }
)

export default service