import Mock from 'mockjs'

const questionList = Mock.mock({
  'list|10-30': [{
    'id|+1': 1,
    title: '@ctitle(10, 20)',
    questionImage: '@image("100x100")',
    questionVideo: '',
    'createBy|+1': 100,
    createName: '@cname',
    createTime: '@datetime',
    'open|1': [true, false],
    'status|1': [0, 1, 2] // 假设 0-待审核, 1-通过, 2-驳回
  }]
}).list

// 题目列表
Mock.mock(/\/web\/question\/pageQueryQuestionList/, 'post', (options) => {
  const { pageNum = 1, pageSize = 10 } = JSON.parse(options.body)
  const list = questionList.slice((pageNum - 1) * pageSize, pageNum * pageSize)
  return {
    code: '200',
    resData: list,
    msg: '查询成功'
  }
})

// 添加题目
Mock.mock(/\/web\/question\/addQuestion/, 'post', () => {
  return {
    code: '200',
    resData: null,
    msg: '添加成功'
  }
})

// 删除题目
Mock.mock('/web/question/deleteQuestion', 'post', () => {
  return {
    code: '200',
    resData: null,
    msg: '删除成功'
  }
})

// 审核题目
Mock.mock('/web/question/auditQuestion', 'post', (options) => {
  const { questionId, status } = JSON.parse(options.body)
  const question = questionList.find(q => q.id === questionId)
  if (question) {
    question.status = status
  }
  return {
    code: '200',
    resData: null,
    msg: '审核成功'
  }
})