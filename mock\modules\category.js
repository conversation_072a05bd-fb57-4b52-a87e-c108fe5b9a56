import Mock from 'mockjs'

const topCategories = [
  { id: 1, name: '掼蛋练习库', questionNum: 15 },
  { id: 2, name: '数学题库', questionNum: 20 },
  { id: 3, name: '英语题库', questionNum: 10 }
]

const subCategories = {
  1: [ // 属于 '掼蛋练习库'
    { id: 101, name: '组牌' },
    { id: 102, name: '开牌' }
  ],
  2: [ // 属于 '数学题库'
    { id: 201, name: '代数' },
    { id: 202, name: '几何' }
  ]
}

// 类型列表
Mock.mock('/web/category/pageQueryCategoryList', 'post', (options) => {
  const { pageNum, pageSize } = JSON.parse(options.body)
  const list = topCategories.slice((pageNum - 1) * pageSize, pageNum * pageSize)
  return {
    code: '200',
    resData: list,
    msg: '查询成功'
  }
})

// 一级类型列表
Mock.mock('/web/category/getTopCategoryList', 'post', () => {
  return {
    code: '200',
    resData: topCategories.map(c => ({ id: c.id, name: c.name })),
    msg: '查询成功'
  }
})

// 二级类型列表
Mock.mock('/web/category/getSubCategoryList', 'post', (options) => {
  const { parentId } = JSON.parse(options.body)
  return {
    code: '200',
    resData: subCategories[parentId] || [],
    msg: '查询成功'
  }
})

// 添加类型
Mock.mock('/web/category/addCategory', 'post', () => {
  return {
    code: '200',
    resData: null,
    msg: '添加成功'
  }
})

// 删除类型
Mock.mock('/web/category/deleteCategory', 'post', () => {
  return {
    code: '200',
    resData: null,
    msg: '删除成功'
  }
})

// 修改类型
Mock.mock('/web/category/updateCategory', 'post', () => {
  return {
    code: '200',
    resData: null,
    msg: '修改成功'
  }
})