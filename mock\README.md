# Mock 模块使用说明

## 概述

本项目使用 `vite-plugin-mock` 和 `mockjs` 来实现前端接口的模拟，方便在没有后端接口的情况下进行前端开发。

## 目录结构

```
mock/
├── index.js              # Mock 主入口文件
├── mockProdServer.js     # 生产环境 Mock 服务器
├── modules/              # Mock 模块目录
│   ├── user.js          # 用户相关接口
│   ├── category.js      # 分类相关接口
│   └── question.js      # 问题相关接口
└── README.md            # 使用说明
```

## 已实现的接口

### 用户模块 (/api/user/*)

- `POST /api/auth/login` - 用户登录
- `GET /api/user/info` - 获取用户信息
- `GET /api/user/list` - 获取用户列表（支持分页和搜索）
- `POST /api/user/add` - 添加用户
- `PUT /api/user/update` - 更新用户
- `DELETE /api/user/delete/:id` - 删除用户
- `POST /api/auth/logout` - 退出登录

### 分类模块 (/api/category/*)

- `GET /api/category/list` - 获取分类列表（支持分页和搜索）
- `GET /api/category/all` - 获取所有分类（不分页）
- `GET /api/category/detail/:id` - 获取分类详情
- `POST /api/category/add` - 添加分类
- `PUT /api/category/update` - 更新分类
- `DELETE /api/category/delete/:id` - 删除分类
- `PUT /api/category/status` - 更新分类状态

### 问题模块 (/api/question/*)

- `GET /api/question/list` - 获取问题列表（支持分页、搜索、分类筛选、状态筛选）
- `GET /api/question/detail/:id` - 获取问题详情
- `POST /api/question/add` - 添加问题
- `PUT /api/question/update` - 更新问题
- `DELETE /api/question/delete/:id` - 删除问题
- `PUT /api/question/status` - 更新问题状态
- `GET /api/question/hot` - 获取热门问题
- `GET /api/question/stats` - 获取问题统计

## 测试账号

- 管理员账号：`admin` / `123456`
- 普通用户账号：`user1` / `123456`

## 使用方法

1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. Mock 接口会自动生效，所有 `/api/*` 的请求都会被拦截并返回模拟数据

3. 在浏览器开发者工具的 Network 面板中可以看到 Mock 请求

## 添加新的 Mock 接口

1. 在 `mock/modules/` 目录下创建新的模块文件
2. 在文件中定义 Mock 接口
3. 在 `mock/index.js` 中导入新模块
4. 在 `mock/mockProdServer.js` 中添加新模块的导入

## 示例：添加新的 Mock 接口

```javascript
// mock/modules/example.js
import Mock from 'mockjs'

Mock.mock('/api/example/list', 'get', () => {
  return {
    code: 200,
    message: '获取成功',
    data: [
      { id: 1, name: '示例1' },
      { id: 2, name: '示例2' }
    ]
  }
})
```

然后在 `mock/index.js` 中添加：
```javascript
import './modules/example'
```

## 注意事项

1. Mock 只在开发环境下生效
2. 所有接口都遵循统一的返回格式：`{ code, message, data }`
3. 成功响应的 code 为 200
4. 错误响应的 code 为对应的 HTTP 状态码
5. 分页接口支持 `page`、`pageSize`、`keyword` 等参数
6. 搜索功能支持模糊匹配

## 关闭 Mock

如果需要连接真实的后端接口，可以：

1. 注释掉 `src/main.js` 中的 Mock 导入
2. 或者设置环境变量 `VITE_USE_MOCK=false` 