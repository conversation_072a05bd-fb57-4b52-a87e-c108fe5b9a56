<template>
  <div class="question-container">
    <div class="page-header">
      <h2>题目管理</h2>
      <el-button type="primary" @click="handleCreate" :icon="Plus">新建题目</el-button>
    </div>

    <el-card>
      <!-- Search area remains the same -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="题目类型">
            <el-select v-model="searchForm.categoryId" placeholder="请选择类型" clearable>
              <el-option
                v-for="item in categoryOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="题目内容">
            <el-input v-model="searchForm.keyword" placeholder="请输入题目内容关键词" />
          </el-form-item>
           <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="已发布" :value="1"></el-option>
              <el-option label="待审核" :value="0"></el-option>
              <el-option label="已禁用" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="categoryName" label="类型" width="120" />
        <el-table-column prop="title" label="题目标题" show-overflow-tooltip />
        <el-table-column prop="answer.values" label="答案" show-overflow-tooltip>
            <template #default="scope">
                {{ scope.row.answer.values.join(', ') }}
            </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 1" type="success">已发布</el-tag>
            <el-tag v-if="scope.row.status === 0" type="warning">待审核</el-tag>
            <el-tag v-if="scope.row.status === 2" type="danger">已禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button
              v-if="userStore.userInfo.role === 'super_admin' && scope.row.status === 0"
              size="small"
              type="success"
              @click="handleApprove(scope.row)"
            >审核通过</el-button>
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新建/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="题目标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入题目标题" />
        </el-form-item>
        <el-form-item label="题目内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入题目内容" />
        </el-form-item>
         <el-form-item label="题目类型" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择类型">
             <template v-for="cat_l1 in categoryOptions" :key="cat_l1.id">
                <el-option-group :label="cat_l1.name">
                    <el-option v-for="cat_l2 in cat_l1.children" :key="cat_l2.id" :label="cat_l2.name" :value="cat_l2.id" />
                </el-option-group>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="答案" prop="answer.values">
            <div v-for="(ans, index) in form.answer.values" :key="index" style="margin-bottom: 10px; display: flex; align-items: center;">
                <el-input v-model="form.answer.values[index]" placeholder="请输入一个正确答案" />
                <el-button @click="removeAnswer(index)" :icon="Delete" circle plain style="margin-left: 10px;"></el-button>
            </div>
            <el-button @click="addAnswer" :icon="Plus">增加答案</el-button>
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="userStore.userInfo.role === 'super_admin'">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">已发布</el-radio>
            <el-radio :label="0">待审核</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { pageQueryQuestionList, addQuestion, updateQuestion, deleteQuestion, auditQuestion } from '@/api/question'
import { pageQueryCategoryList } from '@/api/category'
import { useUserStore } from '@/store/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const userStore = useUserStore()
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

const tableData = ref([])
const categoryOptions = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

const searchForm = reactive({
  categoryId: '',
  keyword: '',
  status: ''
})

const form = reactive({
  id: null,
  title: '',
  content: '',
  categoryId: '',
  answer: {
      type: 'fill-in',
      values: ['']
  },
  status: 0 // Default to pending
})

const formRules = {
  title: [
    { required: true, message: '请输入题目标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入题目内容', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择题目类型', trigger: 'change' }
  ],
  'answer.values': [
    { required: true, message: '至少需要一个答案', trigger: 'blur' },
    { type: 'array', min: 1, message: '至少需要一个答案', trigger: 'change' }
  ]
}

// Fetching data
const fetchList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    if(params.status === '') delete params.status
    const data = await pageQueryQuestionList(params)
    tableData.value = data.list || []
    pagination.total = data.total || 0
  } catch (error) {
    console.error('获取题目列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchCategoryList = async () => {
  try {
    const data = await pageQueryCategoryList()
    // build a tree for selector
    const buildTree = (items, parentId = null) => {
        return items
            .filter(item => item.parentId === parentId)
            .map(item => {
                const children = buildTree(items, item.id);
                if (children.length > 0) {
                    return { ...item, children, hasChildren: true };
                }
                return item;
            });
    };
    categoryOptions.value = buildTree(data.list || [])
  } catch (error) {
    console.error('获取类型列表失败:', error)
  }
}

onMounted(() => {
  fetchList()
  fetchCategoryList()
})


// Form Actions
const handleCreate = () => {
  resetForm()
  dialogTitle.value = '新建题目'
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  dialogTitle.value = '编辑题目'
  // Deep copy to avoid reactivity issues with form
  Object.assign(form, JSON.parse(JSON.stringify(row)))
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    // Contributor submissions should always be pending
    if (userStore.userInfo.role === 'contributor') {
        form.status = 0;
    }
    
    // Find category name to store it
    let categoryName = '';
    for (const l1 of categoryOptions.value) {
        const l2 = l1.children?.find(c => c.id === form.categoryId);
        if (l2) {
            categoryName = l2.name;
            break;
        }
    }
    form.categoryName = categoryName;


    if (form.id) {
      await updateQuestion(form)
      ElMessage.success('更新成功')
    } else {
      await addQuestion(form)
      ElMessage.success('创建成功，等待审核')
    }
    
    dialogVisible.value = false
    fetchList()
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除题目 "${row.title}" 吗?`, 
    '警告',
    { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
  ).then(async () => {
    try {
      await deleteQuestion(row.id)
      ElMessage.success('删除成功')
      fetchList()
    } catch (error) {
      console.error('删除失败:', error)
    }
  })
}

const handleApprove = async (row) => {
    try {
        await auditQuestion({ id: row.id, status: 1 });
        ElMessage.success("审核通过");
        fetchList();
    } catch (error) {
        console.error("审核失败", error);
    }
}


// Answer Management
const addAnswer = () => {
    form.answer.values.push('');
}

const removeAnswer = (index) => {
    if (form.answer.values.length > 1) {
        form.answer.values.splice(index, 1);
    } else {
        ElMessage.warning('至少需要一个答案');
    }
}


// Search and Pagination
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    categoryId: '',
    keyword: '',
    status: ''
  })
  pagination.page = 1
  fetchList()
}

const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.page = 1
  fetchList()
}

const handleCurrentChange = (val) => {
  pagination.page = val
  fetchList()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    id: null,
    title: '',
    content: '',
    categoryId: '',
    answer: {
        type: 'fill-in',
        values: ['']
    },
    status: 0
  })
}

</script>

<style scoped>
.question-container {
  padding: 20px;
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.page-header h2 {
  margin: 0;
  color: #333;
}
.search-area {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
