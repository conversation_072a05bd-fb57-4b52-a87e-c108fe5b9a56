import request from '@/utils/request'

// 类型列表 (分页查询)
export function pageQueryCategoryList(data) {
  return request({
    url: '/category/pageQueryCategoryList',
    method: 'post',
    data
  })
}

// 一级类型列表
export function getTopCategoryList() {
  return request({
    url: '/category/getTopCategoryList',
    method: 'post' // 文档未明确，根据旧版和统一性，使用POST
  })
}

// 二级类型列表
export function getSubCategoryList(data) {
  return request({
    url: '/category/getSubCategoryList',
    method: 'post', // 文档未明确，根据统一性，使用POST
    data // { parentId }
  })
}

// 添加类型
export function addCategory(data) {
  return request({
    url: '/category/addCategory',
    method: 'post',
    data // { categoryName, parentId }
  })
}

// 删除类型
export function deleteCategory(data) {
  return request({
    url: '/category/deleteCategory',
    method: 'post', // 文档未明确，根据统一性，使用POST
    data // { categoryId }
  })
}

// 修改类型
export function updateCategory(data) {
  return request({
    url: '/category/updateCategory',
    method: 'post', // 文档未明确，根据统一性，使用POST
    data // { id, categoryName, parentId }
  })
}