<!-- src/layout/components/Sidebar.vue -->
<template>
  <el-aside width="200px" class="sidebar">
    <div class="logo">
      <h2>刻意练 后台管理</h2>
    </div>
    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      router
    >
      <SidebarItem
        v-for="route in menuRoutes"
        :key="route.path"
        :item="route"
        :base-path="'/'"
      />
    </el-menu>
  </el-aside>
</template>

<!-- src/layout/components/Sidebar.vue -->
<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { usePermissionStore } from '@/store/modules/permission';
import SidebarItem from './SidebarItem.vue';

const route = useRoute();
const permissionStore = usePermissionStore();

// 【核心修改】创建一个计算属性，专门用于提供给菜单
const menuRoutes = computed(() => {
  // 从 permission store 的全量路由中，找到那个作为布局的根路由 (path: '/')
  const layoutRoute = permissionStore.routes.find(r => r.path === '/');
  // 如果找到了，并且它有 children，我们就返回它的 children 数组
  // 这个数组就是我们要展示的一级菜单项列表
  return layoutRoute ? layoutRoute.children : [];
});

const activeMenu = computed(() => {
  const { meta, path } = route;
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});
</script>

<style scoped>
.sidebar {
  background-color: #304156;
  height: 100vh;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b2f3a;
  color: #fff;
}

.logo h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
}
</style>