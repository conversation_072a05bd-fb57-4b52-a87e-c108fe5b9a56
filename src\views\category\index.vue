<template>
  <div class="category-container">
    <div class="page-header">
      <h2>类型管理</h2>
      <el-button type="primary" @click="handleCreate" :icon="Plus">新建类型</el-button>
    </div>

    <el-card>
      <el-table 
        :data="tableData" 
        v-loading="loading" 
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="类型名称" width="250" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="template" label="卡片模板" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新建/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="所属分类" prop="parentId">
          <el-select v-model="form.parentId" placeholder="不选则为一级分类" clearable>
             <el-option v-for="cat in l1Categories" :key="cat.id" :label="cat.name" :value="cat.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="卡片模板" prop="template">
          <el-select v-model="form.template" placeholder="请选择卡片模板">
            <el-option label="风格A" value="style-A"></el-option>
            <el-option label="风格B" value="style-B"></el-option>
            <el-option label="风格C" value="style-C"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入类型描述" 
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { pageQueryQuestionList, addCategory, updateCategory, deleteCategory } from '@/api/category'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

const tableData = ref([])
const allCategories = ref([])

const l1Categories = computed(() => allCategories.value.filter(c => !c.parentId))

const form = reactive({
  id: null,
  name: '',
  description: '',
  parentId: null,
  template: 'style-A',
  status: 1
})

const formRules = {
  name: [
    { required: true, message: '请输入类型名称', trigger: 'blur' }
  ],
  template: [
    { required: true, message: '请选择模板', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入类型描述', trigger: 'blur' }
  ]
}

// 获取列表数据
const fetchList = async () => {
  loading.value = true
  try {
    // API返回的是扁平化列表，在前端构建树
    const data = await pageQueryQuestionList() // Assuming this API now returns all categories
    allCategories.value = data.list || [];
    tableData.value = buildTree(allCategories.value);
  } catch (error) {
    console.error('获取类型列表失败:', error)
    ElMessage.error('获取类型列表失败')
  } finally {
    loading.value = false
  }
}

const buildTree = (items, parentId = null) => {
  return items
    .filter(item => item.parentId === parentId)
    .map(item => {
      const children = buildTree(items, item.id);
      if (children.length > 0) {
        return { ...item, children, hasChildren: true };
      }
      return item;
    });
};

onMounted(() => {
  fetchList()
})

const handleCreate = () => {
  resetForm()
  dialogTitle.value = '新建类型'
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  dialogTitle.value = '编辑类型'
  // Cannot select self or children as parent
  l1Categories.value = allCategories.value.filter(c => !c.parentId && c.id !== row.id);
  Object.assign(form, row)
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (form.id) {
      await updateCategory(form)
      ElMessage.success('更新成功')
    } else {
      await addCategory(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchList()
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除类型 "${row.name}" 吗? 这会删除所有子分类。`, 
    '警告',
    { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
  ).then(async () => {
    try {
      await deleteCategory(row.id)
      ElMessage.success('删除成功')
      fetchList()
    } catch (error) {
      console.error('删除失败:', error)
    }
  }).catch(() => {
    // 用户取消
  })
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    id: null,
    name: '',
    description: '',
    parentId: null,
    template: 'style-A',
    status: 1
  })
  // Restore l1Categories for creation
  l1Categories.value = allCategories.value.filter(c => !c.parentId);
}
</script>

<style scoped>
.category-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
