import request from '@/utils/request'

// 获取验证码
export function getVerifyCode(data) {
  return request({
    // 文档未提供URL，根据登录接口推断
    url: '/login/getVerifyCode',
    method: 'post', // 文档未明确，根据统一性，使用POST
    data // { phone }
  })
}

// web端登录
export function webLogin(data) {
  return request({
    url: '/login/webLogin',
    method: 'post',
    data // { phone, verifyCode }
  })
}

// 用户列表
export function pageQueryUserList(data) {
  return request({
    url: '/user/pageQueryUserList',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(data) {
  return request({
    url: '/user/updateUser',
    method: 'post',
    data // { userId, userName, userRole, perType }
  })
}


export function getUserInfo() {
  return request({
    url: '/user/getUserInfo',
    method: 'get'
  })
}